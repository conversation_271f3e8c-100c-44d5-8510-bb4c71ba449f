#!/usr/bin/env python3
"""
Test the updated Flask API endpoints
"""

import requests
import json

def test_api():
    base_url = "http://127.0.0.1:5000"
    
    print("Testing updated Flask API...")
    
    # Test sentiment analysis endpoint
    test_data = {
        "text": "The food was delicious but the service was terrible. The atmosphere was nice though."
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/analyze",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✓ API endpoint working successfully")
            print(f"Overall sentiment: {result['results']['overall_sentiment']}")
            print(f"Confidence: {result['results']['overall_confidence']}%")
            print(f"Aspects found: {len(result['results']['aspects'])}")
            
            print("\nDetected aspects (without confidence scores):")
            for aspect in result['results']['aspects']:
                print(f"- \"{aspect['aspect']}\" -> {aspect['sentiment']}")
                # Verify confidence is not in the response
                if 'confidence' in aspect:
                    print(f"  WARNING: Confidence still present: {aspect['confidence']}")
                else:
                    print(f"  ✓ No confidence score (as expected)")
            
        else:
            print(f"✗ API request failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"✗ API test failed: {str(e)}")

if __name__ == "__main__":
    test_api()

# BERT Sentiment Analysis Web Application

A modern Flask web application with shadcn/ui components for aspect-based sentiment analysis using the BERT-E2E-ABSA model.

## Features

### Backend
- **Flask API** with sentiment analysis endpoints
- **BERT-E2E-ABSA model integration** for accurate aspect-based sentiment analysis
- **File upload support** for .txt files with drag-and-drop functionality
- **Structured JSON responses** with detailed sentiment breakdown
- **Error handling and validation** for robust operation

### Frontend (shadcn/ui Components)
- **Responsive design** with mobile-first approach
- **Interactive text input** with large textarea component
- **File upload area** with drag-and-drop support and visual feedback
- **Real-time results display** with card-based layout
- **Sentiment visualization** with color-coded indicators and progress bars
- **Smooth animations** and hover effects throughout the interface

### Analysis Features
- **Overall sentiment detection** (positive, negative, neutral)
- **Aspect-based sentiment analysis** showing individual aspects and their sentiments
- **Confidence scores** displayed as percentages (0-100%)
- **Sentiment distribution** with visual progress bars
- **Color-coded system**: Green (positive), Red (negative), Yellow (neutral)

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Setup Instructions

1. **Clone or download the project**
   ```bash
   cd DemoEndToEnd
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify model files**
   Ensure the following files exist in the `models/` directory:
   - `config.json`
   - `pytorch_model.bin`
   - `tokenizer_config.json`
   - `vocab.txt`
   - `special_tokens_map.json`
   - `training_args.bin`

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the application**
   Open your web browser and navigate to: `http://localhost:5000`

## Usage

### Text Input Analysis
1. Enter or paste text in the large textarea
2. Click "Analyze Sentiment" button
3. View results in the right panel showing:
   - Overall sentiment with confidence score
   - Sentiment distribution breakdown
   - Individual aspects detected with their sentiments

### File Upload Analysis
1. Drag and drop a .txt file onto the upload area, or click to browse
2. File information will be displayed (name and size)
3. Click "Analyze Sentiment" to process the file
4. Results will be displayed in the same format as text input

### Interactive Features
- **Clear button**: Removes all input and results
- **File removal**: Click the X button to remove uploaded files
- **Keyboard shortcut**: Ctrl+Enter to analyze text
- **Responsive design**: Works on desktop, tablet, and mobile devices

## API Endpoints

### POST /api/analyze
Analyze text input for sentiment.

**Request:**
```json
{
  "text": "Your text to analyze here"
}
```

**Response:**
```json
{
  "success": true,
  "results": {
    "text": "Your text to analyze here",
    "overall_sentiment": "positive",
    "overall_confidence": 85.3,
    "aspects": [
      {
        "aspect": "product quality",
        "sentiment": "positive",
        "confidence": 0.92,
        "start_pos": 2,
        "end_pos": 4
      }
    ],
    "sentiment_distribution": {
      "positive": 60.0,
      "negative": 20.0,
      "neutral": 20.0
    }
  }
}
```

### POST /api/upload
Upload and analyze a text file.

**Request:** Multipart form data with file field

**Response:** Same format as /api/analyze with additional filename field

## Technical Details

### Architecture
- **Frontend**: HTML5, Tailwind CSS, Vanilla JavaScript
- **Backend**: Flask (Python)
- **Model**: BERT-E2E-ABSA (PyTorch)
- **Styling**: shadcn/ui design system with Tailwind CSS

### File Structure
```
DemoEndToEnd/
├── app.py                          # Main Flask application
├── model_service.py                # BERT model integration
├── text_processor.py               # Text processing utilities
├── requirements.txt                # Python dependencies
├── test_app.py                     # Test suite
├── templates/
│   └── index.html                  # Main HTML template
├── static/
│   ├── css/
│   │   └── style.css              # Custom styles
│   └── js/
│       └── app.js                 # Frontend JavaScript
├── models/                         # BERT model files
├── uploads/                        # Temporary file storage
└── BERT-E2E-ABSA-reduced-code/    # Original model code
```

### Model Configuration
- **Tagging Schema**: BIEOS (Begin, Inside, End, Outside, Single)
- **Architecture**: CNN-BiGRU with BERT base
- **Max Sequence Length**: Dynamic based on input
- **Supported Sentiments**: Positive, Negative, Neutral

## Testing

Run the test suite to verify the application:

```bash
python test_app.py
```

The test suite checks:
- Dependency installation
- Model file presence
- API endpoint functionality
- File upload capabilities

## Troubleshooting

### Common Issues

1. **Model loading errors**
   - Ensure all model files are present in the `models/` directory
   - Check that PyTorch and transformers versions are compatible

2. **File upload issues**
   - Verify the `uploads/` directory exists and is writable
   - Check file size limits (16MB maximum)
   - Ensure only .txt files are uploaded

3. **Performance issues**
   - Model inference runs on CPU by default
   - For GPU acceleration, ensure CUDA is properly installed

### Error Messages
- "No text provided": Enter text in the textarea or upload a file
- "File too large": Reduce file size to under 16MB
- "Only .txt files allowed": Upload a plain text file
- "Model loading failed": Check model files and dependencies

## Browser Compatibility
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License
This project uses the BERT-E2E-ABSA model. Please refer to the original model's license in the `BERT-E2E-ABSA-reduced-code/` directory.

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

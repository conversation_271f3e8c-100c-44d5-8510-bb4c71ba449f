<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BERT Sentiment Analysis</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        border: "hsl(214.3 31.8% 91.4%)",
                        input: "hsl(214.3 31.8% 91.4%)",
                        ring: "hsl(222.2 84% 4.9%)",
                        background: "hsl(0 0% 100%)",
                        foreground: "hsl(222.2 84% 4.9%)",
                        primary: {
                            DEFAULT: "hsl(222.2 47.4% 11.2%)",
                            foreground: "hsl(210 40% 98%)",
                        },
                        secondary: {
                            DEFAULT: "hsl(210 40% 96%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                        destructive: {
                            DEFAULT: "hsl(0 84.2% 60.2%)",
                            foreground: "hsl(210 40% 98%)",
                        },
                        muted: {
                            DEFAULT: "hsl(210 40% 96%)",
                            foreground: "hsl(215.4 16.3% 46.9%)",
                        },
                        accent: {
                            DEFAULT: "hsl(210 40% 96%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                        popover: {
                            DEFAULT: "hsl(0 0% 100%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                        card: {
                            DEFAULT: "hsl(0 0% 100%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                    },
                    borderRadius: {
                        lg: "0.5rem",
                        md: "calc(0.5rem - 2px)",
                        sm: "calc(0.5rem - 4px)",
                    },
                }
            }
        }
    </script>
</head>
<body class="bg-background text-foreground">
    <div class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                            <i data-lucide="brain-circuit" class="w-5 h-5 text-primary-foreground"></i>
                        </div>
                        <h1 class="text-2xl font-bold text-primary">BERT Sentiment Analysis</h1>
                    </div>
                    <div class="text-sm text-muted-foreground">
                        Aspect-Based Sentiment Analysis
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Input Section -->
                <div class="space-y-6">
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h2 class="text-xl font-semibold mb-4 flex items-center">
                            <i data-lucide="edit-3" class="w-5 h-5 mr-2"></i>
                            Text Input
                        </h2>
                        
                        <!-- Text Area -->
                        <div class="space-y-4">
                            <div>
                                <label for="textInput" class="block text-sm font-medium text-foreground mb-2">
                                    Enter text for analysis
                                </label>
                                <textarea
                                    id="textInput"
                                    placeholder="Type or paste your text here for sentiment analysis..."
                                    class="w-full h-32 px-3 py-2 border border-input rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200"
                                ></textarea>
                            </div>
                            
                            <!-- File Upload -->
                            <div>
                                <label class="block text-sm font-medium text-foreground mb-2">
                                    Or upload a text file
                                </label>
                                <div 
                                    id="dropZone"
                                    class="border-2 border-dashed border-input rounded-lg p-6 text-center hover:border-ring transition-colors duration-200 cursor-pointer"
                                >
                                    <div id="dropZoneContent">
                                        <i data-lucide="upload" class="w-8 h-8 mx-auto mb-2 text-muted-foreground"></i>
                                        <p class="text-sm text-muted-foreground mb-1">
                                            Drag and drop a .txt file here, or click to browse
                                        </p>
                                        <p class="text-xs text-muted-foreground">
                                            Maximum file size: 16MB
                                        </p>
                                    </div>
                                    <input type="file" id="fileInput" accept=".txt" class="hidden">
                                </div>
                                <div id="fileInfo" class="mt-2 hidden">
                                    <div class="flex items-center justify-between p-2 bg-secondary rounded-md">
                                        <div class="flex items-center space-x-2">
                                            <i data-lucide="file-text" class="w-4 h-4 text-muted-foreground"></i>
                                            <span id="fileName" class="text-sm font-medium"></span>
                                            <span id="fileSize" class="text-xs text-muted-foreground"></span>
                                        </div>
                                        <button id="removeFile" class="text-destructive hover:text-destructive/80">
                                            <i data-lucide="x" class="w-4 h-4"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex space-x-3">
                                <button
                                    id="analyzeBtn"
                                    class="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                >
                                    <span class="flex items-center justify-center">
                                        <i data-lucide="zap" class="w-4 h-4 mr-2"></i>
                                        Analyze Sentiment
                                    </span>
                                </button>
                                <button
                                    id="clearBtn"
                                    class="px-4 py-2 border border-input rounded-md hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200"
                                >
                                    <i data-lucide="trash-2" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="space-y-6">
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h2 class="text-xl font-semibold mb-4 flex items-center">
                            <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2"></i>
                            Analysis Results
                        </h2>
                        
                        <!-- Loading State -->
                        <div id="loadingState" class="hidden">
                            <div class="flex items-center justify-center py-12">
                                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                                <span class="ml-3 text-muted-foreground">Analyzing sentiment...</span>
                            </div>
                        </div>
                        
                        <!-- Empty State -->
                        <div id="emptyState" class="text-center py-12">
                            <i data-lucide="message-circle" class="w-12 h-12 mx-auto mb-4 text-muted-foreground"></i>
                            <p class="text-muted-foreground">
                                Enter text or upload a file to see sentiment analysis results
                            </p>
                        </div>
                        
                        <!-- Results Content -->
                        <div id="resultsContent" class="hidden space-y-6">
                            <!-- Overall Sentiment -->
                            <div class="p-4 rounded-lg border bg-card">
                                <h3 class="font-semibold mb-3">Overall Sentiment</h3>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div id="sentimentIcon" class="w-8 h-8 rounded-full flex items-center justify-center">
                                            <i data-lucide="smile" class="w-5 h-5"></i>
                                        </div>
                                        <div>
                                            <div id="sentimentLabel" class="font-medium capitalize"></div>
                                            <div id="sentimentConfidence" class="text-sm text-muted-foreground"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Sentiment Distribution -->
                            <div class="p-4 rounded-lg border bg-card">
                                <h3 class="font-semibold mb-3">Sentiment Distribution</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm font-medium text-green-700">Positive</span>
                                        <span id="positivePercent" class="text-sm text-muted-foreground">0%</span>
                                    </div>
                                    <div class="w-full bg-secondary rounded-full h-2">
                                        <div id="positiveBar" class="bg-green-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm font-medium text-red-700">Negative</span>
                                        <span id="negativePercent" class="text-sm text-muted-foreground">0%</span>
                                    </div>
                                    <div class="w-full bg-secondary rounded-full h-2">
                                        <div id="negativeBar" class="bg-red-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm font-medium text-yellow-700">Neutral</span>
                                        <span id="neutralPercent" class="text-sm text-muted-foreground">0%</span>
                                    </div>
                                    <div class="w-full bg-secondary rounded-full h-2">
                                        <div id="neutralBar" class="bg-yellow-500 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Aspects -->
                            <div class="p-4 rounded-lg border bg-card">
                                <h3 class="font-semibold mb-3">Detected Aspects</h3>
                                <div id="aspectsList" class="space-y-2">
                                    <!-- Aspects will be populated here -->
                                </div>
                                <div id="noAspects" class="hidden text-sm text-muted-foreground text-center py-4">
                                    No specific aspects detected in the text
                                </div>
                            </div>
                        </div>
                        
                        <!-- Error State -->
                        <div id="errorState" class="hidden">
                            <div class="p-4 rounded-lg border border-destructive bg-destructive/10">
                                <div class="flex items-center space-x-2">
                                    <i data-lucide="alert-circle" class="w-5 h-5 text-destructive"></i>
                                    <span class="font-medium text-destructive">Error</span>
                                </div>
                                <p id="errorMessage" class="mt-2 text-sm text-destructive"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
    </script>
</body>
</html>

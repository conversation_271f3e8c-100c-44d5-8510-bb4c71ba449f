#!/usr/bin/env python3
"""
Test script for the BERT Sentiment Analysis Flask application
"""

import os
import sys
import requests
import json

def test_flask_app():
    """Test the Flask application endpoints"""
    base_url = "http://localhost:5000"
    
    print("Testing Flask Sentiment Analysis Application")
    print("=" * 50)
    
    # Test 1: Check if the main page loads
    try:
        response = requests.get(f"{base_url}/")
        if response.status_code == 200:
            print("✓ Main page loads successfully")
        else:
            print(f"✗ Main page failed to load (Status: {response.status_code})")
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to Flask app. Make sure it's running on localhost:5000")
        return False
    
    # Test 2: Test sentiment analysis endpoint
    test_text = "I love this product! It's amazing and works perfectly. However, the price is a bit high."
    
    try:
        response = requests.post(
            f"{base_url}/api/analyze",
            json={"text": test_text},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Sentiment analysis endpoint works")
            print(f"  Overall sentiment: {result['results']['overall_sentiment']}")
            print(f"  Confidence: {result['results']['overall_confidence']}%")
            print(f"  Aspects found: {len(result['results']['aspects'])}")
        else:
            print(f"✗ Sentiment analysis failed (Status: {response.status_code})")
            print(f"  Error: {response.text}")
    except Exception as e:
        print(f"✗ Sentiment analysis test failed: {str(e)}")
    
    # Test 3: Test file upload endpoint with a sample file
    try:
        # Create a temporary test file
        test_file_content = "This restaurant has excellent food and great service. The atmosphere is wonderful. But the waiting time is too long and the prices are expensive."
        
        with open("test_sample.txt", "w", encoding="utf-8") as f:
            f.write(test_file_content)
        
        with open("test_sample.txt", "rb") as f:
            files = {"file": ("test_sample.txt", f, "text/plain")}
            response = requests.post(f"{base_url}/api/upload", files=files)
        
        # Clean up test file
        os.remove("test_sample.txt")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ File upload endpoint works")
            print(f"  File processed: {result['filename']}")
            print(f"  Overall sentiment: {result['results']['overall_sentiment']}")
        else:
            print(f"✗ File upload failed (Status: {response.status_code})")
            print(f"  Error: {response.text}")
            
    except Exception as e:
        print(f"✗ File upload test failed: {str(e)}")
    
    print("\nTest completed!")
    return True

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("Checking dependencies...")
    print("-" * 30)
    
    required_packages = [
        "flask",
        "torch",
        "transformers",
        "numpy",
        "werkzeug"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (missing)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nMissing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    print("\nAll dependencies are installed!")
    return True

def check_model_files():
    """Check if model files exist"""
    print("\nChecking model files...")
    print("-" * 30)
    
    model_files = [
        "models/config.json",
        "models/pytorch_model.bin",
        "models/tokenizer_config.json",
        "models/vocab.txt"
    ]
    
    missing_files = []
    
    for file_path in model_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (missing)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\nMissing model files: {', '.join(missing_files)}")
        print("Please ensure the pre-trained model files are in the models/ directory")
        return False
    
    print("\nAll model files are present!")
    return True

if __name__ == "__main__":
    print("BERT Sentiment Analysis App - Test Suite")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    # Check model files
    if not check_model_files():
        print("\nWarning: Some model files are missing. The app may not work correctly.")
    
    # Test the Flask app if it's running
    print("\nTo test the Flask application:")
    print("1. Start the Flask app: python app.py")
    print("2. Run this test: python test_app.py")
    print("\nOr run the test now if the app is already running...")
    
    try:
        test_flask_app()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")

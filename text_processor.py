import os
import re
from typing import List, Optional

class TextProcessor:
    """
    Utility class for processing text files and input validation
    """
    
    ALLOWED_EXTENSIONS = {'txt'}
    MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB
    MAX_TEXT_LENGTH = 10000  # Maximum characters for analysis
    
    def __init__(self):
        pass
    
    def allowed_file(self, filename: str) -> bool:
        """
        Check if the uploaded file has an allowed extension
        
        Args:
            filename (str): Name of the uploaded file
            
        Returns:
            bool: True if file extension is allowed, False otherwise
        """
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.ALLOWED_EXTENSIONS
    
    def extract_text(self, filepath: str) -> str:
        """
        Extract text content from a file
        
        Args:
            filepath (str): Path to the text file
            
        Returns:
            str: Extracted text content
            
        Raises:
            Exception: If file cannot be read or is too large
        """
        try:
            # Check file size
            file_size = os.path.getsize(filepath)
            if file_size > self.MAX_FILE_SIZE:
                raise Exception(f"File too large. Maximum size is {self.MAX_FILE_SIZE / (1024*1024):.1f}MB")
            
            # Read file content
            with open(filepath, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()
            
            # Validate content length
            if len(content) > self.MAX_TEXT_LENGTH:
                content = content[:self.MAX_TEXT_LENGTH]
                print(f"Text truncated to {self.MAX_TEXT_LENGTH} characters")
            
            return content
            
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(filepath, 'r', encoding='latin-1') as file:
                    content = file.read()
                return content
            except Exception as e:
                raise Exception(f"Could not read file: {str(e)}")
        
        except Exception as e:
            raise Exception(f"Error processing file: {str(e)}")
    
    def clean_text(self, text: str) -> str:
        """
        Clean and preprocess text for analysis
        
        Args:
            text (str): Raw text input
            
        Returns:
            str: Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters that might interfere with tokenization
        # Keep basic punctuation for sentiment analysis
        text = re.sub(r'[^\w\s.,!?;:\-\'\"()]', ' ', text)
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        # Strip and return
        return text.strip()
    
    def validate_text_input(self, text: str) -> tuple[bool, Optional[str]]:
        """
        Validate text input for sentiment analysis
        
        Args:
            text (str): Text to validate
            
        Returns:
            tuple: (is_valid, error_message)
        """
        if not text or not text.strip():
            return False, "Text cannot be empty"
        
        if len(text) > self.MAX_TEXT_LENGTH:
            return False, f"Text too long. Maximum length is {self.MAX_TEXT_LENGTH} characters"
        
        # Check for minimum meaningful content
        words = text.split()
        if len(words) < 2:
            return False, "Text should contain at least 2 words for meaningful analysis"
        
        return True, None
    
    def split_long_text(self, text: str, max_length: int = 500) -> List[str]:
        """
        Split long text into smaller chunks for processing
        
        Args:
            text (str): Text to split
            max_length (int): Maximum length per chunk
            
        Returns:
            List[str]: List of text chunks
        """
        if len(text) <= max_length:
            return [text]
        
        chunks = []
        sentences = re.split(r'[.!?]+', text)
        current_chunk = ""
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
                
            # If adding this sentence would exceed max_length, save current chunk
            if len(current_chunk) + len(sentence) > max_length and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence
        
        # Add the last chunk if it exists
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def get_file_info(self, filepath: str) -> dict:
        """
        Get information about an uploaded file
        
        Args:
            filepath (str): Path to the file
            
        Returns:
            dict: File information
        """
        try:
            stat = os.stat(filepath)
            return {
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'name': os.path.basename(filepath),
                'extension': filepath.rsplit('.', 1)[1].lower() if '.' in filepath else ''
            }
        except Exception as e:
            return {'error': str(e)}

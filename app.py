from flask import Flask, render_template, request, jsonify
import os
import json
from werkzeug.utils import secure_filename
from model_service import <PERSON>timent<PERSON><PERSON>yzer
from text_processor import TextProcessor

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize services
sentiment_analyzer = SentimentAnalyzer()
text_processor = TextProcessor()

@app.route('/')
def index():
    """Render the main application page"""
    return render_template('index.html')

@app.route('/api/analyze', methods=['POST'])
def analyze_sentiment():
    """API endpoint for sentiment analysis"""
    try:
        data = request.get_json()

        if not data or 'text' not in data:
            return jsonify({'error': 'No text provided'}), 400

        text = data['text'].strip()
        if not text:
            return jsonify({'error': 'Empty text provided'}), 400

        # Perform sentiment analysis
        results = sentiment_analyzer.analyze(text)

        return jsonify({
            'success': True,
            'results': results
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """API endpoint for file upload and analysis"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        if not text_processor.allowed_file(file.filename):
            return jsonify({'error': 'Only .txt files are allowed'}), 400

        # Save and process file
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        # Extract text from file
        text = text_processor.extract_text(filepath)

        # Clean up uploaded file
        os.remove(filepath)

        if not text.strip():
            return jsonify({'error': 'File is empty or could not be read'}), 400

        # Perform sentiment analysis
        results = sentiment_analyzer.analyze(text)

        return jsonify({
            'success': True,
            'filename': filename,
            'results': results
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)

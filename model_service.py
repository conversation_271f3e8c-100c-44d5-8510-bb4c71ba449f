import torch
import numpy as np
import os
import sys
from transformers import BertConfig, BertTokenizer

# Add the BERT-E2E-ABSA-reduced-code directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'BERT-E2E-ABSA-reduced-code'))

from absa_layer import Bert<PERSON><PERSON><PERSON><PERSON>
from glue_utils import convert_examples_to_seq_features, InputExample, ABSAProcessor
from seq_utils import ot2bieos_ts, bio2ot_ts, tag2ts

class SentimentAnalyzer:
    def __init__(self, model_path='./models', tagging_schema='BIEOS'):
        """
        Initialize the sentiment analyzer with pre-trained BERT-E2E-ABSA model
        
        Args:
            model_path (str): Path to the model directory
            tagging_schema (str): Tagging schema to use (BIEOS, BIO, or OT)
        """
        self.model_path = model_path
        self.tagging_schema = tagging_schema
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Initialize processor and load model
        self.processor = ABSAProcessor()
        self.label_list = self.processor.get_labels(self.tagging_schema)
        self.label_map = {label: i for i, label in enumerate(self.label_list)}
        self.id2label = {i: label for i, label in enumerate(self.label_list)}
        
        self._load_model()
    
    def _load_model(self):
        """Load the pre-trained model and tokenizer"""
        try:
            # Load tokenizer
            self.tokenizer = BertTokenizer.from_pretrained(self.model_path)
            
            # Load model configuration
            config = BertConfig.from_pretrained(self.model_path)
            config.num_labels = len(self.label_list)
            config.absa_type = 'cnn_bigru'  # Default architecture
            config.tfm_mode = 'finetune'
            config.fix_tfm = 0
            
            # Load model
            self.model = BertABSATagger.from_pretrained(self.model_path, config=config)
            self.model.to(self.device)
            self.model.eval()
            
            print(f"Model loaded successfully on {self.device}")
            
        except Exception as e:
            print(f"Error loading model: {e}")
            raise e
    
    def _preprocess_text(self, text):
        """
        Preprocess text for model input
        
        Args:
            text (str): Input text to analyze
            
        Returns:
            dict: Preprocessed features for model input
        """
        # Create a dummy example for processing
        words = text.split()
        dummy_labels = ['O'] * len(words)  # Dummy labels for inference
        
        example = InputExample(
            guid="inference-0",
            text_a=text,
            text_b=None,
            label=dummy_labels
        )
        
        # Convert to features
        features = convert_examples_to_seq_features(
            examples=[example],
            label_list=self.label_list,
            tokenizer=self.tokenizer,
            cls_token_at_end=False,
            cls_token=self.tokenizer.cls_token,
            sep_token=self.tokenizer.sep_token,
            cls_token_segment_id=0,
            pad_on_left=False,
            pad_token_segment_id=0
        )
        
        return features[0], words
    
    def _postprocess_predictions(self, predictions, words, evaluate_label_ids):
        """
        Convert model predictions to structured output
        
        Args:
            predictions (np.array): Model predictions
            words (list): Original words
            evaluate_label_ids (np.array): Mapping between words and tokens
            
        Returns:
            dict: Structured sentiment analysis results
        """
        # Get predicted labels for original words
        pred_labels = predictions[evaluate_label_ids]
        pred_tags = [self.id2label[label_id] for label_id in pred_labels]
        
        # Convert tagging schema if needed
        if self.tagging_schema == 'OT':
            pred_tags = ot2bieos_ts(pred_tags)
        elif self.tagging_schema == 'BIO':
            pred_tags = ot2bieos_ts(bio2ot_ts(pred_tags))
        
        # Extract aspect-sentiment pairs
        ts_sequence = tag2ts(ts_tag_sequence=pred_tags)
        
        aspects = []
        overall_sentiment_counts = {'positive': 0, 'negative': 0, 'neutral': 0}
        
        for beg, end, sentiment in ts_sequence:
            aspect_text = ' '.join(words[beg:end+1])
            sentiment_label = sentiment.lower()
            
            # Map sentiment labels
            if sentiment_label == 'pos':
                sentiment_label = 'positive'
                overall_sentiment_counts['positive'] += 1
            elif sentiment_label == 'neg':
                sentiment_label = 'negative'
                overall_sentiment_counts['negative'] += 1
            elif sentiment_label == 'neu':
                sentiment_label = 'neutral'
                overall_sentiment_counts['neutral'] += 1
            
            aspects.append({
                'aspect': aspect_text,
                'sentiment': sentiment_label,
                'start_pos': beg,
                'end_pos': end
            })
        
        # Determine overall sentiment
        total_aspects = sum(overall_sentiment_counts.values())
        if total_aspects == 0:
            overall_sentiment = 'neutral'
            overall_confidence = 0.5
        else:
            dominant_sentiment = max(overall_sentiment_counts, key=overall_sentiment_counts.get)
            overall_sentiment = dominant_sentiment
            overall_confidence = overall_sentiment_counts[dominant_sentiment] / total_aspects
        
        return {
            'text': ' '.join(words),
            'overall_sentiment': overall_sentiment,
            'overall_confidence': round(overall_confidence * 100, 1),
            'aspects': aspects,
            'sentiment_distribution': {
                'positive': round((overall_sentiment_counts['positive'] / max(total_aspects, 1)) * 100, 1),
                'negative': round((overall_sentiment_counts['negative'] / max(total_aspects, 1)) * 100, 1),
                'neutral': round((overall_sentiment_counts['neutral'] / max(total_aspects, 1)) * 100, 1)
            }
        }
    
    def analyze(self, text):
        """
        Perform sentiment analysis on input text
        
        Args:
            text (str): Input text to analyze
            
        Returns:
            dict: Sentiment analysis results
        """
        try:
            # Preprocess text
            features, words = self._preprocess_text(text)
            
            # Prepare model inputs
            input_ids = torch.tensor([features.input_ids], dtype=torch.long).to(self.device)
            attention_mask = torch.tensor([features.input_mask], dtype=torch.long).to(self.device)
            token_type_ids = torch.tensor([features.segment_ids], dtype=torch.long).to(self.device)
            
            # Run inference
            with torch.no_grad():
                inputs = {
                    'input_ids': input_ids,
                    'attention_mask': attention_mask,
                    'token_type_ids': token_type_ids
                }
                outputs = self.model(**inputs)

                # Handle different output formats
                if isinstance(outputs, tuple) and len(outputs) > 1:
                    logits = outputs[1] if len(outputs) > 1 else outputs[0]
                else:
                    logits = outputs[0] if isinstance(outputs, tuple) else outputs

                # Get predictions
                if self.model.tagger_config.absa_type != 'crf':
                    predictions = np.argmax(logits.detach().cpu().numpy(), axis=-1)[0]
                else:
                    predictions = self.model.tagger.viterbi_tags(logits=logits, mask=attention_mask)[0]
            
            # Postprocess predictions
            results = self._postprocess_predictions(predictions, words, features.evaluate_label_ids)
            
            return results
            
        except Exception as e:
            print(f"Error during analysis: {e}")
            raise e

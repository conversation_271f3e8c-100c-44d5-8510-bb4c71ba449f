#!/usr/bin/env python3
"""
Test the updated UI format
"""

import requests

def test_ui():
    print('Testing updated UI format...')
    
    try:
        response = requests.post(
            'http://127.0.0.1:5000/api/analyze',
            json={'text': 'The Thai curry was terrible but the pad thai was delicious and the service was great.'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print('✓ API working correctly')
            print(f'Aspects found: {len(result["results"]["aspects"])}')
            
            print('\nExpected UI format:')
            for aspect in result['results']['aspects']:
                print(f'Aspect Span: "{aspect["aspect"]}"')
                print(f'Sentiment: {aspect["sentiment"]} (with colored badge)')
                print()
        else:
            print(f'✗ API error: {response.status_code}')
            
    except Exception as e:
        print(f'✗ Test failed: {str(e)}')

if __name__ == "__main__":
    test_ui()

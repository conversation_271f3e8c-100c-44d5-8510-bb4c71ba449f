/* Custom styles for BERT Sentiment Analysis App */

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.2s ease-in-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: hsl(210 40% 96%);
}

::-webkit-scrollbar-thumb {
    background: hsl(215.4 16.3% 46.9%);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: hsl(222.2 84% 4.9%);
}

/* Enhanced button hover effects */
.btn-primary {
    @apply bg-primary text-primary-foreground px-4 py-2 rounded-md;
    @apply hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    @apply transition-all duration-200 transform hover:scale-105;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
}

.btn-secondary {
    @apply px-4 py-2 border border-input rounded-md;
    @apply hover:bg-accent hover:text-accent-foreground;
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
    @apply transition-all duration-200;
}

/* File upload drop zone animations */
.drop-zone-active {
    @apply border-primary bg-primary/5;
    transform: scale(1.02);
}

.drop-zone-dragover {
    @apply border-primary bg-primary/10;
    transform: scale(1.05);
}

/* Sentiment color classes */
.sentiment-positive {
    @apply bg-green-100 text-green-800 border-green-200;
}

.sentiment-negative {
    @apply bg-red-100 text-red-800 border-red-200;
}

.sentiment-neutral {
    @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

/* Sentiment icons */
.sentiment-icon-positive {
    @apply bg-green-100 text-green-600;
}

.sentiment-icon-negative {
    @apply bg-red-100 text-red-600;
}

.sentiment-icon-neutral {
    @apply bg-yellow-100 text-yellow-600;
}

/* Aspect cards */
.aspect-card {
    @apply p-3 rounded-lg border transition-all duration-200;
    @apply hover:shadow-md hover:scale-105;
}

.aspect-card.positive {
    @apply bg-green-50 border-green-200 hover:bg-green-100;
}

.aspect-card.negative {
    @apply bg-red-50 border-red-200 hover:bg-red-100;
}

.aspect-card.neutral {
    @apply bg-yellow-50 border-yellow-200 hover:bg-yellow-100;
}

/* Progress bars animation */
.progress-bar {
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading animation */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Fade in animation for results */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide up animation */
.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse animation for loading states */
.pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom focus styles */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}

/* Text area enhancements */
textarea.enhanced {
    @apply resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent;
    @apply transition-all duration-200;
    min-height: 120px;
}

textarea.enhanced:focus {
    box-shadow: 0 0 0 2px hsl(222.2 84% 4.9% / 0.2);
}

/* Card hover effects */
.card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

/* Confidence score styling */
.confidence-score {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.confidence-high {
    @apply bg-green-100 text-green-800;
}

.confidence-medium {
    @apply bg-yellow-100 text-yellow-800;
}

.confidence-low {
    @apply bg-red-100 text-red-800;
}

/* Mobile responsiveness enhancements */
@media (max-width: 640px) {
    .mobile-stack {
        @apply flex-col space-y-3 space-x-0;
    }
    
    .mobile-full {
        @apply w-full;
    }
    
    .mobile-text-sm {
        @apply text-sm;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        @apply bg-slate-900 text-slate-100;
    }
}

/* Error state styling */
.error-border {
    @apply border-destructive focus:ring-destructive;
}

/* Success state styling */
.success-border {
    @apply border-green-500 focus:ring-green-500;
}

/* File upload styling */
.file-upload-area {
    @apply border-2 border-dashed border-input rounded-lg p-6 text-center;
    @apply hover:border-ring transition-colors duration-200 cursor-pointer;
}

.file-upload-area.dragover {
    @apply border-primary bg-primary/5;
}

/* Tooltip styling */
.tooltip {
    @apply absolute z-10 px-2 py-1 text-xs font-medium text-white bg-gray-900 rounded shadow-lg;
    @apply opacity-0 invisible transition-all duration-200;
}

.tooltip.show {
    @apply opacity-100 visible;
}

/* Custom animations for sentiment analysis */
.sentiment-reveal {
    animation: sentimentReveal 0.6s ease-out forwards;
}

@keyframes sentimentReveal {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05) translateY(-5px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

// BERT Sentiment Analysis App JavaScript

class SentimentAnalysisApp {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.currentFile = null;
    }

    initializeElements() {
        // Input elements
        this.textInput = document.getElementById('textInput');
        this.fileInput = document.getElementById('fileInput');
        this.dropZone = document.getElementById('dropZone');
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.clearBtn = document.getElementById('clearBtn');
        
        // File info elements
        this.fileInfo = document.getElementById('fileInfo');
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');
        this.removeFile = document.getElementById('removeFile');
        
        // Result elements
        this.loadingState = document.getElementById('loadingState');
        this.emptyState = document.getElementById('emptyState');
        this.resultsContent = document.getElementById('resultsContent');
        this.errorState = document.getElementById('errorState');
        this.errorMessage = document.getElementById('errorMessage');
        
        // Sentiment display elements
        this.sentimentIcon = document.getElementById('sentimentIcon');
        this.sentimentLabel = document.getElementById('sentimentLabel');
        this.sentimentConfidence = document.getElementById('sentimentConfidence');
        
        // Distribution elements
        this.positivePercent = document.getElementById('positivePercent');
        this.negativePercent = document.getElementById('negativePercent');
        this.neutralPercent = document.getElementById('neutralPercent');
        this.positiveBar = document.getElementById('positiveBar');
        this.negativeBar = document.getElementById('negativeBar');
        this.neutralBar = document.getElementById('neutralBar');
        
        // Aspects elements
        this.aspectsList = document.getElementById('aspectsList');
        this.noAspects = document.getElementById('noAspects');
    }

    bindEvents() {
        // Text input events
        this.textInput.addEventListener('input', () => this.onTextChange());
        
        // File upload events
        this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        this.dropZone.addEventListener('click', () => this.fileInput.click());
        this.dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
        this.dropZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        this.dropZone.addEventListener('drop', (e) => this.handleDrop(e));
        
        // Button events
        this.analyzeBtn.addEventListener('click', () => this.analyzeText());
        this.clearBtn.addEventListener('click', () => this.clearAll());
        this.removeFile.addEventListener('click', () => this.removeFileSelection());
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.analyzeText();
            }
        });
    }

    onTextChange() {
        const hasText = this.textInput.value.trim().length > 0;
        this.updateAnalyzeButton();
    }

    updateAnalyzeButton() {
        const hasText = this.textInput.value.trim().length > 0;
        const hasFile = this.currentFile !== null;
        this.analyzeBtn.disabled = !hasText && !hasFile;
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        this.dropZone.classList.add('drop-zone-dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        this.dropZone.classList.remove('drop-zone-dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        this.dropZone.classList.remove('drop-zone-dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processFile(files[0]);
        }
    }

    processFile(file) {
        // Validate file type
        if (!file.name.toLowerCase().endsWith('.txt')) {
            this.showError('Please select a .txt file');
            return;
        }

        // Validate file size (16MB limit)
        if (file.size > 16 * 1024 * 1024) {
            this.showError('File size must be less than 16MB');
            return;
        }

        this.currentFile = file;
        this.showFileInfo(file);
        this.updateAnalyzeButton();
    }

    showFileInfo(file) {
        this.fileName.textContent = file.name;
        this.fileSize.textContent = `(${this.formatFileSize(file.size)})`;
        this.fileInfo.classList.remove('hidden');
    }

    removeFileSelection() {
        this.currentFile = null;
        this.fileInput.value = '';
        this.fileInfo.classList.add('hidden');
        this.updateAnalyzeButton();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    async analyzeText() {
        try {
            this.showLoading();
            
            let textToAnalyze = '';
            
            if (this.currentFile) {
                // Upload and analyze file
                const formData = new FormData();
                formData.append('file', this.currentFile);
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.error || 'File upload failed');
                }
                
                this.displayResults(result.results);
                
            } else {
                // Analyze text input
                textToAnalyze = this.textInput.value.trim();
                
                if (!textToAnalyze) {
                    throw new Error('Please enter some text to analyze');
                }
                
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ text: textToAnalyze })
                });
                
                const result = await response.json();
                
                if (!response.ok) {
                    throw new Error(result.error || 'Analysis failed');
                }
                
                this.displayResults(result.results);
            }
            
        } catch (error) {
            this.showError(error.message);
        }
    }

    showLoading() {
        this.hideAllStates();
        this.loadingState.classList.remove('hidden');
        this.analyzeBtn.disabled = true;
    }

    displayResults(results) {
        this.hideAllStates();
        this.resultsContent.classList.remove('hidden');
        this.resultsContent.classList.add('fade-in');
        
        // Display overall sentiment
        this.displayOverallSentiment(results);
        
        // Display sentiment distribution
        this.displaySentimentDistribution(results.sentiment_distribution);
        
        // Display aspects
        this.displayAspects(results.aspects);
        
        this.analyzeBtn.disabled = false;
    }

    displayOverallSentiment(results) {
        const sentiment = results.overall_sentiment;
        const confidence = results.overall_confidence;
        
        // Update sentiment label and confidence
        this.sentimentLabel.textContent = sentiment;
        this.sentimentConfidence.textContent = `${confidence}% confidence`;
        
        // Update icon and styling
        this.updateSentimentIcon(sentiment);
    }

    updateSentimentIcon(sentiment) {
        // Clear existing classes
        this.sentimentIcon.className = 'w-8 h-8 rounded-full flex items-center justify-center';
        
        // Add sentiment-specific styling
        if (sentiment === 'positive') {
            this.sentimentIcon.classList.add('sentiment-icon-positive');
            this.sentimentIcon.innerHTML = '<i data-lucide="smile" class="w-5 h-5"></i>';
        } else if (sentiment === 'negative') {
            this.sentimentIcon.classList.add('sentiment-icon-negative');
            this.sentimentIcon.innerHTML = '<i data-lucide="frown" class="w-5 h-5"></i>';
        } else {
            this.sentimentIcon.classList.add('sentiment-icon-neutral');
            this.sentimentIcon.innerHTML = '<i data-lucide="meh" class="w-5 h-5"></i>';
        }
        
        // Re-initialize Lucide icons
        lucide.createIcons();
    }

    displaySentimentDistribution(distribution) {
        // Update percentages
        this.positivePercent.textContent = `${distribution.positive}%`;
        this.negativePercent.textContent = `${distribution.negative}%`;
        this.neutralPercent.textContent = `${distribution.neutral}%`;
        
        // Animate progress bars
        setTimeout(() => {
            this.positiveBar.style.width = `${distribution.positive}%`;
            this.negativeBar.style.width = `${distribution.negative}%`;
            this.neutralBar.style.width = `${distribution.neutral}%`;
        }, 100);
    }

    displayAspects(aspects) {
        this.aspectsList.innerHTML = '';
        
        if (aspects.length === 0) {
            this.noAspects.classList.remove('hidden');
            return;
        }
        
        this.noAspects.classList.add('hidden');
        
        aspects.forEach((aspect, index) => {
            const aspectCard = this.createAspectCard(aspect);
            aspectCard.style.animationDelay = `${index * 0.1}s`;
            this.aspectsList.appendChild(aspectCard);
        });
    }

    createAspectCard(aspect) {
        const card = document.createElement('div');
        card.className = `aspect-card ${aspect.sentiment} slide-up`;
        
        card.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <div class="font-medium text-sm">${this.escapeHtml(aspect.aspect)}</div>
                    <div class="text-xs text-muted-foreground mt-1">
                        ${aspect.sentiment} sentiment
                    </div>
                </div>
                <div class="ml-3">
                    <span class="confidence-score confidence-${this.getConfidenceLevel(aspect.confidence)}">
                        ${Math.round(aspect.confidence * 100)}%
                    </span>
                </div>
            </div>
        `;
        
        return card;
    }

    getConfidenceLevel(confidence) {
        if (confidence >= 0.8) return 'high';
        if (confidence >= 0.6) return 'medium';
        return 'low';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showError(message) {
        this.hideAllStates();
        this.errorState.classList.remove('hidden');
        this.errorMessage.textContent = message;
        this.analyzeBtn.disabled = false;
    }

    hideAllStates() {
        this.loadingState.classList.add('hidden');
        this.emptyState.classList.add('hidden');
        this.resultsContent.classList.add('hidden');
        this.errorState.classList.add('hidden');
    }

    clearAll() {
        this.textInput.value = '';
        this.removeFileSelection();
        this.hideAllStates();
        this.emptyState.classList.remove('hidden');
        this.updateAnalyzeButton();
    }
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SentimentAnalysisApp();
});

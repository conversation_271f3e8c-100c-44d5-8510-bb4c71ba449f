#!/usr/bin/env python3
"""
Test script for the updated aspect display functionality
"""

import sys
sys.path.append('.')

from model_service import SentimentAnalyzer

def test_aspects():
    print('Testing updated sentiment analysis...')
    
    analyzer = SentimentAnalyzer()
    
    # Test with a sample text that should have multiple aspects
    text = "The food was delicious but the service was terrible. The atmosphere was nice though."
    
    result = analyzer.analyze(text)
    
    print(f'Overall sentiment: {result["overall_sentiment"]}')
    print(f'Aspects found: {len(result["aspects"])}')
    print('Detected aspects:')
    
    for aspect in result['aspects']:
        print(f'- "{aspect["aspect"]}" -> {aspect["sentiment"]}')
    
    print('\nTest completed successfully!')

if __name__ == "__main__":
    test_aspects()
